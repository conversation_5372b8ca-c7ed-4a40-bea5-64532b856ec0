import { <PERSON>, Button, Chip, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { AttendanceLog } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllAttendanceLog } from "../api/getAllAttendanceLog";
import { AttendanceTypeOptions, attendanceStatusOptions } from "../constants";
import { useBulkActionAttendanceLog } from "../hooks/useBulkActionAttendanceLog";

const AttendanceLogListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionAttendanceLog({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_attendance_logs.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	const columns: ColumnDef<
		AttendanceLog & {
			userName: string;
			userEmail: string;
			worksiteName: string;
		}
	>[] = [
			{
				accessorKey: "userName",
				header: ({ column }) => (
					<ColumnHeader column={column} title="Nama Karyawan" />
				),
				cell: (info) => info.getValue(),
				enableColumnFilter: true,
				meta: { columnLabel: "Nama Karyawan", filterVariant: "textSearch" },
			},
			{
				accessorKey: "userEmail",
			header: ({ column }) => <ColumnHeader column={column} title="Email" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			meta: { columnLabel: "Email", filterVariant: "textSearch" },
		},
		{
			accessorKey: "worksiteName",
				header: ({ column }) => (
					<ColumnHeader column={column} title="Lokasi Kerja" />
				),
				cell: (info) => info.getValue(),
				enableColumnFilter: false,
				meta: { columnLabel: "Lokasi Kerja" },
			},
			{
				accessorKey: "type",
				header: ({ column }) => <ColumnHeader column={column} title="Tipe" />,
				cell: ({ row }) => {
					const value = row.original.type;

					return (
						<Chip
						label={
							AttendanceTypeOptions.find((type) => type.value === value)?.label || "unknown"
					}
						color="default"
						size="small"
					/>);
				},
				enableColumnFilter: true,
				meta: {
					columnLabel: "Tipe",
					filterVariant: "select",
					selectOptions: AttendanceTypeOptions,
					isMultipleSelect: true,
				},
			},
			{
				accessorKey: "logDate",
				header: ({ column }) => <ColumnHeader column={column} title="Tanggal" />,
				cell: (info) => info.getValue(),
				enableColumnFilter: true,
				meta: { columnLabel: "Tanggal", filterVariant: "dateRange" },
			},
			{
				accessorKey: "logTime",
				header: ({ column }) => <ColumnHeader column={column} title="Jam" />,
				cell: (info) => info.getValue(),
				enableColumnFilter: false,
				meta: { columnLabel: "Jam" },
			},
			{
				accessorKey: "status",
				header: ({ column }) => <ColumnHeader column={column} title="Status" />,
				cell: ({ row }) => {
					const value = row.original.status;

					return (
						<Chip
						label={
							attendanceStatusOptions.find((type) => type.value === value)?.label || "unknown"
					}
				color={value === "ON_TIME" ? "success" : "primary"}
						size="small"
					/>
				);},
				enableColumnFilter: true,
				meta: {
					columnLabel: "Status",
					filterVariant: "select",
					selectOptions: attendanceStatusOptions,
				},
			},
			{
				accessorKey: "locationLat",
				header: ({ column }) => <ColumnHeader column={column} title="Lat" />,
				cell: (info) => info.getValue(),
				enableColumnFilter: false,
				meta: { columnLabel: "Lat" },
			},
			{
				accessorKey: "locationLong",
				header: ({ column }) => <ColumnHeader column={column} title="Long" />,
				cell: (info) => info.getValue(),
				enableColumnFilter: false,
				meta: { columnLabel: "Long" },
			},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Absensi
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					columns={columns}
					fetchData={getAllAttendanceLog}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) => navigate(`/attendance/${data.id}/edit`)}
							viewTitle="Detail Absensi"
							renderDetail={(data) => (
								<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
									<Stack
										direction="column"
										spacing={2}
										sx={{ mt: 1, textAlign: "right" }}
									>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">
												Nama karyawan
											</Typography>
											<Typography>{data.userName}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Email</Typography>
											<Typography>{data.userEmail}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Lokasi Kerja</Typography>
											<Typography>{data.worksiteName}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">
												Tipe Kehadiran
											</Typography>
											<Typography>
												{AttendanceTypeOptions.find(
													(type) => type.value === data.type,
												)?.label || "unknown"}
											</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Tanggal</Typography>
											<Typography>{data.logDate}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Jam</Typography>
											<Typography>{data.logTime}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Status</Typography>
											<Typography>
												{attendanceStatusOptions.find(
													(type) => type.value === data.status,
												)?.label || "unknown"}
											</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Latitude</Typography>
											<Typography>{data.locationLat}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">Longitude</Typography>
											<Typography>{data.locationLong}</Typography>
										</Stack>
										<Stack direction="row" justifyContent="space-between">
											<Typography color="textDisabled">
												Distance Score (Semakin kecil, semakin mirip)
											</Typography>
											<Typography>{data.distanceScore}</Typography>
										</Stack>
										<Stack
											direction="column"
											justifyContent="start"
											spacing={2}
										>
											<Typography
												color="textDisabled"
												sx={{ textAlign: "left" }}
											>
												Foto Kehadiran
											</Typography>
											<Box
												sx={{
													borderRadius: 1,
													overflow: "hidden",
													display: "inline-block",
													aspectRatio: "16/9",
												}}
											>
												<img
													src={row.original.photo}
													alt={row.original.userName}
													style={{
														width: "100%",
														height: "100%",
														objectFit: "cover",
													}}
												/>
											</Box>
										</Stack>
									</Stack>
								</Stack>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Absensi
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/attendance/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default AttendanceLogListPage;
