import { <PERSON>, Button, <PERSON>, Stack, Typography } from "@mui/material";
import type { ColumnDef } from "@tanstack/react-table";
import { useState } from "react";
import { Link as ReactRouterLink, useNavigate } from "react-router";
import IconsaxAddSquareIcon from "@/assets/icons/iconsax-add-square.svg?react";
import IconsaxReceiveSquareIcon from "@/assets/icons/iconsax-receive-square.svg?react";
import { DataTable } from "@/shared/components/common/DataTable";
import { ColumnHeader } from "@/shared/components/common/DataTable/ColumnHeader";
import { RowActions } from "@/shared/components/common/DataTable/RowActions";
import { ViewOptions } from "@/shared/components/common/DataTable/ViewOption";
import { Footer } from "@/shared/components/common/Footer";
import { IconWrapper } from "@/shared/components/common/IconWrapper";
import { useBulkActionHandler } from "@/shared/hooks/useBulkActionHandler";
import { useMutationCallbacks } from "@/shared/hooks/useMutationCallback";
import type { ViolationResponse } from "@/shared/types/api";
import type { SelectionData } from "@/shared/types/common";
import { getAllViolation } from "../api/getAllViolation";
import { useBulkActionViolation } from "../hooks/useBulkActionViolation";

const ViolationListPage: React.FC = () => {
	const navigate = useNavigate();
	const [selectionData, setSelectionData] = useState<SelectionData>({
		selectedIds: [],
		excludedIds: [],
		isSelectAll: false,
		selectionInfo: { selectedCount: 0, totalCount: 0, isSelectAll: false },
	});

	const columns: ColumnDef<ViolationResponse>[] = [
		{
			accessorKey: "userName",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Nama User" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Nama User", filterVariant: "textSearch" },
		},
		{
			accessorKey: "userEmail",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Email User" />
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Email User", filterVariant: "textSearch" },
		},
		{
			accessorKey: "name",
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Jenis Pelanggaran"
				/>
			),
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Jenis Pelanggaran", filterVariant: "textSearch" },
		},
		{
			accessorKey: "penaltyPoints",
			accessorFn: (row) => row.penaltyPoints?.toString(),
			header: ({ column }) => (
				<ColumnHeader
					sx={{ minWidth: 200 }}
					column={column}
					title="Poin Penalti"
				/>
			),
			cell: (info) => {
				const value = Number(info.getValue() || 0) as number;
				return (
					<Chip
						label={`${value} poin`}
						color={value >= 20 ? "error" : value >= 10 ? "warning" : "default"}
						size="small"
					/>
				);
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Poin Penalti", filterVariant: "numberRange" },
		},
		{
			accessorKey: "punishment",
			header: ({ column }) => <ColumnHeader column={column} title="Hukuman" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Hukuman", filterVariant: "textSearch" },
		},
		{
			accessorKey: "violationDate",
			header: ({ column }) => (
				<ColumnHeader column={column} title="Tanggal Pelanggaran" />
			),
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Tanggal Pelanggaran", filterVariant: "dateRange" },
		},
		{
			accessorKey: "recorderName",
			header: ({ column }) => <ColumnHeader column={column} title="Recorder" />,
			cell: (info) => info.getValue(),
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Recorder", filterVariant: "textSearch" },
		},
		{
			accessorKey: "createdAt",
			header: ({ column }) => <ColumnHeader column={column} title="Dibuat" />,
			cell: (info) => {
				const value = info.getValue() as string;
				return new Date(value).toLocaleDateString("id-ID");
			},
			enableColumnFilter: true,
			enableSorting: true,
			meta: { columnLabel: "Dibuat", filterVariant: "dateRange" },
		},
	];

	const { handleSuccess, handleError } = useMutationCallbacks();
	const bulkActionMutation = useBulkActionViolation({
		onSuccessCallback: (data) => {
			if (data instanceof Blob) {
				// Download CSV
				const url = window.URL.createObjectURL(data);
				const a = document.createElement("a");
				a.href = url;
				a.download = "exported_violations.csv";
				document.body.appendChild(a);
				a.click();
				a.remove();
				window.URL.revokeObjectURL(url);
			} else {
				handleSuccess(data.message);
			}
		},
		onErrorCallback: handleError,
	});

	const handleBulkAction = useBulkActionHandler(
		bulkActionMutation,
		selectionData,
	);

	return (
		<Box sx={{ pb: 10 }}>
			<Typography variant="h6" component="h2" sx={{ mb: 3 }}>
				Manajemen Pelanggaran
			</Typography>
			<Box
				sx={{
					backgroundColor: "background.paper",
					borderRadius: 2,
					p: 4,
					overflow: "auto",
				}}
			>
				<DataTable
					columns={columns}
					fetchData={getAllViolation}
					enableSelection
					onSelectionChange={setSelectionData}
					renderRowActions={(row) => (
						<RowActions
							row={row}
							onEdit={(data) => navigate(`/violations/${data.id}/edit`)}
							viewTitle="Detail Pelanggaran"
							renderDetail={(data) => (
								<>
									<Stack direction="column" spacing={2} sx={{ mt: 1 }}>
										<Stack
											direction="column"
											spacing={2}
											sx={{ mt: 1, textAlign: "right" }}
										>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Nama User</Typography>
												<Typography>{data.userName}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Email User</Typography>
												<Typography>{data.userEmail}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Jenis Pelanggaran
												</Typography>
												<Typography>{data.name}</Typography>
											</Stack>
											<Stack direction="column" textAlign="left">
												<Typography color="textDisabled">Deskripsi</Typography>
												<Typography>{data.description || "-"}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Poin Penalti
												</Typography>
												<Typography>{data.penaltyPoints} poin</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Hukuman</Typography>
												<Typography>{data.punishment}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Tanggal Pelanggaran
												</Typography>
												<Typography>
													{new Date(data.violationDate).toLocaleDateString(
														"id-ID",
													)}
												</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">Recorder</Typography>
												<Typography>{data.recorderName}</Typography>
											</Stack>
											<Stack direction="row" justifyContent="space-between">
												<Typography color="textDisabled">
													Email Recorder
												</Typography>
												<Typography>{data.recorderEmail}</Typography>
											</Stack>
											<Stack direction="column" textAlign="left">
												<Typography color="textDisabled">Catatan</Typography>
												<Typography>{data.notes || "-"}</Typography>
											</Stack>
										</Stack>
									</Stack>
								</>
							)}
						/>
					)}
					toolbar={({ hideableColumns, onDeselectAll }) => {
						return (
							<Stack
								direction="row"
								alignItems="center"
								justifyContent="space-between"
								sx={{ mb: 4 }}
							>
								<Box>
									<Typography variant="subtitle1">
										Tabel Manajemen Pelanggaran
									</Typography>
								</Box>
								<Stack direction="row" spacing={2} alignItems="center">
									<Button
										component={ReactRouterLink}
										to="/violations/new"
										variant="contained"
										color="primary"
										startIcon={<IconWrapper icon={IconsaxAddSquareIcon} />}
									>
										Tambah Data
									</Button>
									<Button
										variant="outlined"
										color="inherit"
										startIcon={<IconWrapper icon={IconsaxReceiveSquareIcon} />}
										onClick={() => handleBulkAction("export", onDeselectAll)}
									>
										Export Data
									</Button>
									<ViewOptions hideableColumns={hideableColumns} />
								</Stack>
							</Stack>
						);
					}}
				/>
			</Box>
			<Box sx={{ mt: 4 }}>
				<Footer />
			</Box>
		</Box>
	);
};

export default ViolationListPage;
